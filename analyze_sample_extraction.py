#!/usr/bin/env python3
"""
Analyze the sample screen_1_49.json file to understand current extraction results
and demonstrate how the improved extraction logic would work.
"""

import json
import sys
from typing import Dict, Any, List
from collections import defaultdict

def load_sample_data(file_path: str) -> Dict[str, Any]:
    """Load the sample JSON file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        sys.exit(1)

def extract_assets_from_processed_json(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract all assets from the processed JSON structure"""
    assets = []
    
    def traverse(node: Dict[str, Any], parent_name: str = ""):
        if not isinstance(node, dict):
            return
        
        # Check if this node is an asset
        node_type = node.get("type", "")
        figma_type = node.get("figma_type", "")
        name = node.get("name", "")
        node_id = node.get("id", "")
        
        if node_type in ["icon", "component", "image"] or figma_type in ["VECTOR", "IMAGE", "COMPONENT", "INSTANCE"]:
            dimensions = node.get("dimensions", {})
            width = dimensions.get("width", 0)
            height = dimensions.get("height", 0)
            
            assets.append({
                "id": node_id,
                "name": name,
                "type": node_type,
                "figma_type": figma_type,
                "width": width,
                "height": height,
                "parent_context": parent_name,
                "area": width * height if width and height else 0
            })
        
        # Traverse children
        children = node.get("children", [])
        if isinstance(children, list):
            for child in children:
                traverse(child, name)
    
    # Start traversal from root
    if "root" in data:
        traverse(data["root"])
    
    return assets

def analyze_assets(assets: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze the extracted assets"""
    analysis = {
        "total_count": len(assets),
        "by_type": defaultdict(int),
        "by_figma_type": defaultdict(int),
        "by_size_category": defaultdict(int),
        "name_patterns": defaultdict(int),
        "size_distribution": [],
        "potential_issues": []
    }
    
    for asset in assets:
        # Count by type
        analysis["by_type"][asset["type"]] += 1
        analysis["by_figma_type"][asset["figma_type"]] += 1
        
        # Size categorization
        width, height = asset["width"], asset["height"]
        max_dim = max(width, height) if width and height else 0
        
        if max_dim == 0:
            analysis["by_size_category"]["unknown"] += 1
        elif max_dim <= 16:
            analysis["by_size_category"]["tiny (≤16px)"] += 1
        elif max_dim <= 32:
            analysis["by_size_category"]["small (≤32px)"] += 1
        elif max_dim <= 64:
            analysis["by_size_category"]["medium (≤64px)"] += 1
        elif max_dim <= 128:
            analysis["by_size_category"]["large (≤128px)"] += 1
        else:
            analysis["by_size_category"]["extra large (>128px)"] += 1
        
        # Name pattern analysis
        name = asset["name"].lower()
        if "vector" in name:
            analysis["name_patterns"]["generic_vector"] += 1
        elif "ellipse" in name:
            analysis["name_patterns"]["ellipse"] += 1
        elif "rectangle" in name:
            analysis["name_patterns"]["rectangle"] += 1
        elif "line" in name:
            analysis["name_patterns"]["line"] += 1
        elif any(icon_word in name for icon_word in ["heart", "star", "home", "user", "search", "comment", "settings"]):
            analysis["name_patterns"]["meaningful_icon_names"] += 1
        else:
            analysis["name_patterns"]["other"] += 1
        
        # Identify potential issues
        if "vector" in name.lower() and asset["type"] == "icon":
            analysis["potential_issues"].append(f"Generic vector name: {asset['name']} (ID: {asset['id']})")
        
        if width == 0 or height == 0:
            analysis["potential_issues"].append(f"Zero dimension: {asset['name']} ({width}x{height})")
        
        # Store size info for distribution
        analysis["size_distribution"].append({
            "name": asset["name"],
            "width": width,
            "height": height,
            "area": asset["area"]
        })
    
    return analysis

def print_analysis_report(analysis: Dict[str, Any], assets: List[Dict[str, Any]]):
    """Print a comprehensive analysis report"""
    print("🔍 FIGMA ASSET EXTRACTION ANALYSIS")
    print("=" * 50)
    print()
    
    # Overall stats
    print(f"📊 OVERALL STATISTICS")
    print("-" * 25)
    print(f"Total Assets Found: {analysis['total_count']}")
    print()
    
    # By type
    print("📋 BY ASSET TYPE:")
    for asset_type, count in analysis["by_type"].items():
        print(f"  {asset_type}: {count}")
    print()
    
    # By Figma type
    print("🎨 BY FIGMA TYPE:")
    for figma_type, count in analysis["by_figma_type"].items():
        print(f"  {figma_type}: {count}")
    print()
    
    # By size
    print("📏 BY SIZE CATEGORY:")
    for size_cat, count in analysis["by_size_category"].items():
        print(f"  {size_cat}: {count}")
    print()
    
    # Name patterns
    print("🏷️  NAME PATTERNS:")
    for pattern, count in analysis["name_patterns"].items():
        print(f"  {pattern}: {count}")
    print()
    
    # Sample assets by category
    print("📝 SAMPLE ASSETS BY CATEGORY:")
    print("-" * 35)
    
    # Group assets by type for sampling
    by_type = defaultdict(list)
    for asset in assets:
        by_type[asset["type"]].append(asset)
    
    for asset_type, type_assets in by_type.items():
        print(f"\n{asset_type.upper()} ASSETS (showing first 5):")
        for i, asset in enumerate(type_assets[:5]):
            print(f"  {i+1}. {asset['name']} ({asset['width']}x{asset['height']}px)")
            print(f"     ID: {asset['id']}, Figma Type: {asset['figma_type']}")
        if len(type_assets) > 5:
            print(f"     ... and {len(type_assets) - 5} more")
    
    # Potential issues
    if analysis["potential_issues"]:
        print(f"\n⚠️  POTENTIAL ISSUES ({len(analysis['potential_issues'])}):")
        print("-" * 25)
        for issue in analysis["potential_issues"][:10]:  # Show first 10
            print(f"  • {issue}")
        if len(analysis["potential_issues"]) > 10:
            print(f"  ... and {len(analysis['potential_issues']) - 10} more issues")
    
    print()

def demonstrate_improved_logic(assets: List[Dict[str, Any]]):
    """Demonstrate how the improved extraction logic would categorize these assets"""
    print("🚀 IMPROVED EXTRACTION LOGIC ANALYSIS")
    print("=" * 45)
    print()
    
    # Simulate improved categorization
    improved_categories = {
        "meaningful_icons": [],
        "generic_vectors": [],
        "ui_components": [],
        "large_graphics": [],
        "potential_images": []
    }
    
    for asset in assets:
        name = asset["name"].lower()
        width, height = asset["width"], asset["height"]
        max_dim = max(width, height) if width and height else 0
        figma_type = asset["figma_type"]
        
        # Apply improved categorization logic
        if any(icon_name in name for icon_name in ["heart", "star", "home", "user", "search", "comment", "settings", "sliders"]):
            improved_categories["meaningful_icons"].append(asset)
        elif "vector" in name and max_dim <= 32:
            improved_categories["generic_vectors"].append(asset)
        elif figma_type in ["COMPONENT", "INSTANCE"] and max_dim <= 64:
            improved_categories["ui_components"].append(asset)
        elif max_dim > 100:
            improved_categories["large_graphics"].append(asset)
        elif "ellipse" in name or "rectangle" in name:
            improved_categories["potential_images"].append(asset)
    
    # Report improved categorization
    print("📊 IMPROVED CATEGORIZATION:")
    for category, items in improved_categories.items():
        print(f"  {category.replace('_', ' ').title()}: {len(items)}")
        if items:
            print(f"    Examples: {', '.join([item['name'] for item in items[:3]])}")
            if len(items) > 3:
                print(f"    ... and {len(items) - 3} more")
        print()
    
    # Recommendations
    print("💡 RECOMMENDATIONS:")
    print("-" * 20)
    
    meaningful_icons = len(improved_categories["meaningful_icons"])
    generic_vectors = len(improved_categories["generic_vectors"])
    
    if meaningful_icons > 0:
        print(f"✅ Good: Found {meaningful_icons} meaningful icons with descriptive names")
    
    if generic_vectors > 0:
        print(f"⚠️  Consider: {generic_vectors} generic 'Vector' elements might need better naming")
    
    large_graphics = len(improved_categories["large_graphics"])
    if large_graphics > 0:
        print(f"📸 Note: {large_graphics} large graphics found - these might be images or illustrations")
    
    ui_components = len(improved_categories["ui_components"])
    if ui_components > 0:
        print(f"🧩 Good: {ui_components} UI components identified - these are likely reusable elements")
    
    print()

def main():
    """Main function"""
    file_path = "screen_1_49.json"
    
    print(f"Loading sample file: {file_path}")
    data = load_sample_data(file_path)
    
    print("Extracting assets from processed JSON...")
    assets = extract_assets_from_processed_json(data)
    
    print("Analyzing assets...")
    analysis = analyze_assets(assets)
    
    # Print comprehensive report
    print_analysis_report(analysis, assets)
    
    # Demonstrate improved logic
    demonstrate_improved_logic(assets)
    
    # Save detailed results
    results = {
        "analysis": dict(analysis),
        "assets": assets
    }
    
    with open("sample_analysis_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print("💾 Detailed results saved to: sample_analysis_results.json")

if __name__ == "__main__":
    main()
