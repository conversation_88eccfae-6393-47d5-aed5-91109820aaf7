#!/usr/bin/env python3
"""
Test the improved Figma extraction logic without full module dependencies.
This demonstrates the core improvements made to the extraction functions.
"""

import re
from typing import Dict, Any, List

# Replicate the improved regex pattern
IMPROVED_ICON_NAME_REGEX = re.compile(
    r"(?i)(^ic[-_ ]|\bicon\b|^mdi[-_ ]|^ion[-_ ]|^fa[\w-]*|\bheroicon\b|\bmaterial\b|\blogo\b|"
    r"\bglyph\b|\bbrand\b|\bsvg\b|\blucide\b|\bfeather\b|\btabler\b|\bpi-|\bti-|\bri-|\buil\b|\bbx\b|"
    r"\biconify\b|\beva\b|\bant\b|\bantdesign\b|\bbootstrap-icons\b|\bbi-|\bocticon\b|"
    r"\bsimpleicons\b|\bremixicon\b|\bmi-|\bsi-|\barrow\b|\bchevron\b|\bplus\b|\bminus\b|"
    r"\bclose\b|\bx\b|\bcheck\b|\btick\b|\bstar\b|\bheart\b|\buser\b|\bprofile\b|\bavatar\b|"
    r"\bhome\b|\bmenu\b|\bhamburger\b|\bsearch\b|\bfilter\b|\bsort\b|\bedit\b|\bdelete\b|"
    r"\btrash\b|\bsettings\b|\bgear\b|\bcog\b|\bbell\b|\bnotification\b|\bmessage\b|\bchat\b|"
    r"\bmail\b|\bemail\b|\bphone\b|\bcall\b|\bplay\b|\bpause\b|\bstop\b|\bvolume\b|\bmute\b|"
    r"\bshare\b|\bdownload\b|\bupload\b|\bsave\b|\bprint\b|\bcopy\b|\bpaste\b|\bcut\b|"
    r"\bundo\b|\bredo\b|\brefresh\b|\breload\b|\bsync\b|\bcloud\b|\bfolder\b|\bfile\b|"
    r"\bdocument\b|\bimage\b|\bphoto\b|\bcamera\b|\bvideo\b|\bmusic\b|\baudio\b|\bmap\b|"
    r"\blocation\b|\bpin\b|\bmarker\b|\bcalendar\b|\bdate\b|\btime\b|\bclock\b|\btimer\b|"
    r"\balarm\b|\bstopwatch\b|\bbattery\b|\bwifi\b|\bsignal\b|\bbluetooth\b|\busb\b|"
    r"\bpower\b|\benergy\b|\blight\b|\bdark\b|\bsun\b|\bmoon\b|\bweather\b|\btemperature\b|"
    r"\bthermometer\b|\bfan\b|\bair\b|\bwater\b|\bfire\b|\bearth\b|\bplanet\b|\bglobe\b|"
    r"\bworld\b|\bmap\b|\bcompass\b|\bdirection\b|\bnavigation\b|\bgps\b|\broute\b|\bpath\b|"
    r"\bway\b|\bguide\b|\bhelp\b|\binfo\b|\bquestion\b|\bexclamation\b|\bwarning\b|\berror\b|"
    r"\bsuccess\b|\bfail\b|\bcancel\b|\bconfirm\b|\baccept\b|\breject\b|\bapprove\b|\bdeny\b|"
    r"\blike\b|\bdislike\b|\bthumb\b|\bfavorite\b|\bbookmark\b|\btag\b|\blabel\b|\bflag\b|"
    r"\bbadge\b|\bmedal\b|\btrophy\b|\baward\b|\bprize\b|\bgift\b|\bpresent\b|\bbox\b|"
    r"\bpackage\b|\bdelivery\b|\bshipping\b|\btruck\b|\bcar\b|\bbus\b|\btrain\b|\bplane\b|"
    r"\bship\b|\bboat\b|\bbike\b|\bwalk\b|\brun\b|\bsport\b|\bgame\b|\bplay\b|\btoy\b|"
    r"\bpuzzle\b|\bdice\b|\bcard\b|\bchess\b|\bboard\b|\btarget\b|\bbullseye\b|\bgoal\b|"
    r"\bflag\b|\bfinish\b|\bstart\b|\bbegin\b|\bend\b|\bstop\b|\bpause\b|\bcontinue\b|"
    r"\bnext\b|\bprev\b|\bforward\b|\bback\b|\breturn\b|\bexit\b|\benter\b|\bgo\b|"
    r"\bmove\b|\bshift\b|\btransfer\b|\bswap\b|\bexchange\b|\btrade\b|\bbuy\b|\bsell\b|"
    r"\bpay\b|\bmoney\b|\bcoin\b|\bdollar\b|\beuro\b|\bpound\b|\byen\b|\bcurrency\b|"
    r"\bbank\b|\bcard\b|\bcredit\b|\bdebit\b|\bwallet\b|\bpurse\b|\bbag\b|\bcart\b|"
    r"\bbasket\b|\bshop\b|\bstore\b|\bmarket\b|\bmall\b|\bbusiness\b|\boffice\b|\bwork\b|"
    r"\bjob\b|\btask\b|\btodo\b|\blist\b|\bcheck\b|\btick\b|\bcross\b|\bx\b|\bplus\b|\bminus\b)"
)

# Original regex for comparison
ORIGINAL_ICON_NAME_REGEX = re.compile(
    r"(?i)(^ic[-_ ]|\bicon\b|^mdi[-_ ]|^ion[-_ ]|^fa[\w-]*|\bheroicon\b|\bmaterial\b|\blogo\b)"
)

def test_icon_name_recognition():
    """Test the improved icon name recognition"""
    print("🏷️  ICON NAME RECOGNITION TEST")
    print("=" * 40)
    print()
    
    # Test cases from your sample file and common icon names
    test_names = [
        # From your sample (should be recognized)
        "settings-sliders", "search", "heart", "star", "home", "user", "comment",
        
        # Common UI icons (should be recognized with improvements)
        "arrow-right", "chevron-down", "plus", "minus", "close", "check", "x",
        "menu", "hamburger", "filter", "sort", "edit", "delete", "trash",
        "bell", "notification", "message", "chat", "mail", "phone", "play",
        "pause", "download", "upload", "share", "save", "copy", "refresh",
        
        # Icon library prefixes (should be recognized)
        "lucide-heart", "feather-star", "tabler-home", "heroicon-user",
        "fa-search", "mdi-settings", "ion-menu", "ic_close",
        
        # Generic names (should NOT be recognized as icons)
        "Vector", "Line 1", "Ellipse 1", "Rectangle 65", "Background", 
        "Container", "Frame", "Group", "Text", "Button",
        
        # Borderline cases
        "arrow", "chevron", "plus", "minus", "close", "check", "star", "heart"
    ]
    
    print("📊 COMPARISON: Original vs Improved Recognition")
    print("-" * 50)
    
    original_recognized = []
    improved_recognized = []
    
    for name in test_names:
        original_match = bool(ORIGINAL_ICON_NAME_REGEX.search(name))
        improved_match = bool(IMPROVED_ICON_NAME_REGEX.search(name))
        
        if original_match:
            original_recognized.append(name)
        if improved_match:
            improved_recognized.append(name)
        
        status = ""
        if improved_match and not original_match:
            status = "✅ NEW"
        elif improved_match and original_match:
            status = "✅ BOTH"
        elif original_match and not improved_match:
            status = "❌ LOST"
        else:
            status = "❌ NONE"
        
        print(f"  {name:<20} | Original: {original_match:<5} | Improved: {improved_match:<5} | {status}")
    
    print()
    print(f"📈 SUMMARY:")
    print(f"  Original regex recognized: {len(original_recognized)}/{len(test_names)} ({len(original_recognized)/len(test_names)*100:.1f}%)")
    print(f"  Improved regex recognized: {len(improved_recognized)}/{len(test_names)} ({len(improved_recognized)/len(test_names)*100:.1f}%)")
    print(f"  New icons captured: {len(set(improved_recognized) - set(original_recognized))}")
    print()

def analyze_sample_with_improved_logic():
    """Analyze how the improved logic would handle your sample data"""
    print("🔍 SAMPLE DATA ANALYSIS WITH IMPROVED LOGIC")
    print("=" * 50)
    print()
    
    # Assets from your sample file
    sample_assets = [
        {"name": "settings-sliders", "type": "INSTANCE", "width": 24, "height": 24},
        {"name": "search", "type": "INSTANCE", "width": 24, "height": 24},
        {"name": "heart", "type": "INSTANCE", "width": 24, "height": 24},
        {"name": "star", "type": "INSTANCE", "width": 16, "height": 16},
        {"name": "home", "type": "INSTANCE", "width": 24, "height": 24},
        {"name": "user", "type": "INSTANCE", "width": 24, "height": 24},
        {"name": "comment", "type": "INSTANCE", "width": 24, "height": 24},
        {"name": "Vector", "type": "VECTOR", "width": 24, "height": 7},
        {"name": "Vector", "type": "VECTOR", "width": 24, "height": 24},
        {"name": "Vector", "type": "VECTOR", "width": 16, "height": 15},
        {"name": "Ellipse 1", "type": "VECTOR", "width": 110, "height": 110},
        {"name": "Ellipse 2", "type": "VECTOR", "width": 27, "height": 27},
        {"name": "Rectangle 65", "type": "VECTOR", "width": 456, "height": 80},
        {"name": "Line 1", "type": "VECTOR", "width": 16, "height": 1},
        {"name": "Line 2", "type": "VECTOR", "width": 0, "height": 16}
    ]
    
    # Categorize using improved logic
    categories = {
        "meaningful_icons": [],
        "ui_components": [],
        "large_graphics": [],
        "generic_vectors": [],
        "potential_exclusions": []
    }
    
    for asset in sample_assets:
        name = asset["name"]
        asset_type = asset["type"]
        width = asset["width"]
        height = asset["height"]
        max_dim = max(width, height) if width and height else 0
        
        # Apply improved categorization
        if IMPROVED_ICON_NAME_REGEX.search(name):
            if asset_type in ["INSTANCE", "COMPONENT"]:
                categories["ui_components"].append(asset)
            else:
                categories["meaningful_icons"].append(asset)
        elif max_dim > 100:
            categories["large_graphics"].append(asset)
        elif "vector" in name.lower() and max_dim <= 32:
            categories["generic_vectors"].append(asset)
        elif any(exclude_word in name.lower() for exclude_word in ["background", "container", "frame"]):
            categories["potential_exclusions"].append(asset)
        else:
            categories["generic_vectors"].append(asset)
    
    print("📊 IMPROVED CATEGORIZATION RESULTS:")
    print("-" * 40)
    
    for category, assets in categories.items():
        print(f"\n{category.replace('_', ' ').title()}: {len(assets)}")
        for asset in assets:
            print(f"  • {asset['name']} ({asset['width']}x{asset['height']}px, {asset['type']})")
    
    print()
    
    # Show filtering recommendations
    print("💡 FILTERING RECOMMENDATIONS:")
    print("-" * 35)
    
    meaningful_count = len(categories["meaningful_icons"]) + len(categories["ui_components"])
    generic_count = len(categories["generic_vectors"])
    large_count = len(categories["large_graphics"])
    
    print(f"✅ Keep: {meaningful_count} meaningful icons/components")
    print(f"⚠️  Review: {generic_count} generic vectors (may need better naming)")
    print(f"📸 Consider: {large_count} large graphics (might be images/illustrations)")
    
    if categories["potential_exclusions"]:
        print(f"🚫 Exclude: {len(categories['potential_exclusions'])} structural elements")
    
    print()

def demonstrate_filtering_options():
    """Demonstrate the new filtering options"""
    print("🎛️  FILTERING OPTIONS DEMONSTRATION")
    print("=" * 40)
    print()
    
    print("The improved extraction now supports:")
    print()
    
    print("1. 📏 SIZE-BASED FILTERING:")
    print("   • max_icon_size: 512px (increased from 256px)")
    print("   • min_size: Configurable minimum size")
    print("   • Icons can be up to 4x larger for vectors/logos")
    print()
    
    print("2. 🏷️  TYPE-BASED FILTERING:")
    print("   • include_images: Photo-like content")
    print("   • include_icons: UI elements, symbols")
    print("   • include_components: Reusable design elements")
    print()
    
    print("3. 🚫 EXCLUSION PATTERNS:")
    print("   • Default exclusions: background, container, frame, divider")
    print("   • Custom regex patterns for specific needs")
    print("   • Smart detection of structural vs. asset elements")
    print()
    
    print("4. 📊 ENHANCED METADATA:")
    print("   • Dimensions and area calculations")
    print("   • Export settings detection")
    print("   • Image fill identification")
    print("   • Better asset categorization")
    print()
    
    print("EXAMPLE USAGE:")
    print("```python")
    print("# Get only meaningful icons, exclude generic vectors")
    print("icons_only = get_figma_assets_data_filtered(")
    print("    figma_link, access_token,")
    print("    include_images=False,")
    print("    include_components=False,")
    print("    include_icons=True,")
    print("    min_size=16,")
    print("    exclude_patterns=[r'\\bvector\\b', r'\\bline\\b', r'\\bellipse\\b']")
    print(")")
    print("```")
    print()

def main():
    """Main function"""
    print("🚀 FIGMA EXTRACTION IMPROVEMENTS TEST")
    print("=" * 45)
    print()
    
    test_icon_name_recognition()
    print()
    analyze_sample_with_improved_logic()
    print()
    demonstrate_filtering_options()
    
    print("🎯 CONCLUSION:")
    print("-" * 15)
    print("The improved extraction logic should significantly reduce")
    print("unwanted figures while capturing more important icons!")
    print()
    print("Key improvements for your use case:")
    print("✅ Better recognition of UI icons (settings, search, heart, etc.)")
    print("✅ Filtering out generic 'Vector' elements")
    print("✅ Smart size-based categorization")
    print("✅ Exclusion of structural elements")
    print("✅ Enhanced metadata for better decision making")

if __name__ == "__main__":
    main()
