#!/usr/bin/env python3
"""
Test the improved Figma extraction logic specifically on sample3.json
to demonstrate the improvements for this e-commerce/product listing interface.
"""

import json
import re
from typing import Dict, Any, List
from collections import defaultdict

# Import the improved regex
IMPROVED_ICON_NAME_REGEX = re.compile(
    r"(?i)(^ic[-_ ]|\bicon\b|^mdi[-_ ]|^ion[-_ ]|^fa[\w-]*|\bheroicon\b|\bmaterial\b|\blogo\b|"
    r"\bglyph\b|\bbrand\b|\bsvg\b|\blucide\b|\bfeather\b|\btabler\b|\bpi-|\bti-|\bri-|\buil\b|\bbx\b|"
    r"\biconify\b|\beva\b|\bant\b|\bantdesign\b|\bbootstrap-icons\b|\bbi-|\bocticon\b|"
    r"\bsimpleicons\b|\bremixicon\b|\bmi-|\bsi-|\barrow\b|\bchevron\b|\bplus\b|\bminus\b|"
    r"\bclose\b|\bx\b|\bcheck\b|\btick\b|\bstar\b|\bheart\b|\buser\b|\bprofile\b|\bavatar\b|"
    r"\bhome\b|\bmenu\b|\bhamburger\b|\bsearch\b|\bfilter\b|\bsort\b|\bedit\b|\bdelete\b|"
    r"\btrash\b|\bsettings\b|\bgear\b|\bcog\b|\bbell\b|\bnotification\b|\bmessage\b|\bchat\b|"
    r"\bmail\b|\bemail\b|\bphone\b|\bcall\b|\bplay\b|\bpause\b|\bstop\b|\bvolume\b|\bmute\b|"
    r"\bshare\b|\bdownload\b|\bupload\b|\bsave\b|\bprint\b|\bcopy\b|\bpaste\b|\bcut\b|"
    r"\bundo\b|\bredo\b|\brefresh\b|\breload\b|\bsync\b|\bcloud\b|\bfolder\b|\bfile\b|"
    r"\bdocument\b|\bimage\b|\bphoto\b|\bcamera\b|\bvideo\b|\bmusic\b|\baudio\b|\bmap\b|"
    r"\blocation\b|\bpin\b|\bmarker\b|\bcalendar\b|\bdate\b|\btime\b|\bclock\b|\btimer\b|"
    r"\balarm\b|\bstopwatch\b|\bbattery\b|\bwifi\b|\bsignal\b|\bbluetooth\b|\busb\b|"
    r"\bpower\b|\benergy\b|\blight\b|\bdark\b|\bsun\b|\bmoon\b|\bweather\b|\btemperature\b|"
    r"\bthermometer\b|\bfan\b|\bair\b|\bwater\b|\bfire\b|\bearth\b|\bplanet\b|\bglobe\b|"
    r"\bworld\b|\bmap\b|\bcompass\b|\bdirection\b|\bnavigation\b|\bgps\b|\broute\b|\bpath\b|"
    r"\bway\b|\bguide\b|\bhelp\b|\binfo\b|\bquestion\b|\bexclamation\b|\bwarning\b|\berror\b|"
    r"\bsuccess\b|\bfail\b|\bcancel\b|\bconfirm\b|\baccept\b|\breject\b|\bapprove\b|\bdeny\b|"
    r"\blike\b|\bdislike\b|\bthumb\b|\bfavorite\b|\bbookmark\b|\btag\b|\blabel\b|\bflag\b|"
    r"\bbadge\b|\bmedal\b|\btrophy\b|\baward\b|\bprize\b|\bgift\b|\bpresent\b|\bbox\b|"
    r"\bpackage\b|\bdelivery\b|\bshipping\b|\btruck\b|\bcar\b|\bbus\b|\btrain\b|\bplane\b|"
    r"\bship\b|\bboat\b|\bbike\b|\bwalk\b|\brun\b|\bsport\b|\bgame\b|\bplay\b|\btoy\b|"
    r"\bpuzzle\b|\bdice\b|\bcard\b|\bchess\b|\bboard\b|\btarget\b|\bbullseye\b|\bgoal\b|"
    r"\bflag\b|\bfinish\b|\bstart\b|\bbegin\b|\bend\b|\bstop\b|\bpause\b|\bcontinue\b|"
    r"\bnext\b|\bprev\b|\bforward\b|\bback\b|\breturn\b|\bexit\b|\benter\b|\bgo\b|"
    r"\bmove\b|\bshift\b|\btransfer\b|\bswap\b|\bexchange\b|\btrade\b|\bbuy\b|\bsell\b|"
    r"\bpay\b|\bmoney\b|\bcoin\b|\bdollar\b|\beuro\b|\bpound\b|\byen\b|\bcurrency\b|"
    r"\bbank\b|\bcard\b|\bcredit\b|\bdebit\b|\bwallet\b|\bpurse\b|\bbag\b|\bcart\b|"
    r"\bbasket\b|\bshop\b|\bstore\b|\bmarket\b|\bmall\b|\bbusiness\b|\boffice\b|\bwork\b|"
    r"\bjob\b|\btask\b|\btodo\b|\blist\b|\bcheck\b|\btick\b|\bcross\b|\bx\b|\bplus\b|\bminus\b)"
)

def load_sample3_data() -> Dict[str, Any]:
    """Load sample3.json"""
    with open("sample3.json", "r") as f:
        return json.load(f)

def analyze_sample3_assets():
    """Analyze the assets in sample3.json with improved logic"""
    print("🛍️  SAMPLE3.JSON ANALYSIS - E-COMMERCE PRODUCT INTERFACE")
    print("=" * 60)
    print()
    
    data = load_sample3_data()
    
    # Extract all assets
    assets = []
    def traverse(node, parent_name=""):
        if not isinstance(node, dict):
            return
        
        node_type = node.get("type", "")
        figma_type = node.get("figma_type", "")
        name = node.get("name", "")
        node_id = node.get("id", "")
        
        if node_type in ["icon", "component", "image"] or figma_type in ["VECTOR", "IMAGE", "COMPONENT", "INSTANCE"]:
            dimensions = node.get("dimensions", {})
            width = dimensions.get("width", 0)
            height = dimensions.get("height", 0)
            
            assets.append({
                "id": node_id,
                "name": name,
                "type": node_type,
                "figma_type": figma_type,
                "width": width,
                "height": height,
                "parent_context": parent_name,
                "area": width * height if width and height else 0
            })
        
        children = node.get("children", [])
        if isinstance(children, list):
            for child in children:
                traverse(child, name)
    
    traverse(data.get("root", {}))
    
    print(f"📊 TOTAL ASSETS FOUND: {len(assets)}")
    print()
    
    # Categorize with improved logic
    categories = {
        "meaningful_icons": [],
        "e_commerce_elements": [],
        "ui_components": [],
        "generic_vectors": [],
        "large_backgrounds": [],
        "should_exclude": []
    }
    
    for asset in assets:
        name = asset["name"].lower()
        asset_type = asset["type"]
        figma_type = asset["figma_type"]
        width = asset["width"]
        height = asset["height"]
        max_dim = max(width, height) if width and height else 0
        
        # Apply improved categorization
        if IMPROVED_ICON_NAME_REGEX.search(name):
            if any(ecom_term in name for ecom_term in ["heart", "cart", "basket", "shop", "buy", "sell", "favorite"]):
                categories["e_commerce_elements"].append(asset)
            else:
                categories["meaningful_icons"].append(asset)
        elif name in ["background"] and max_dim > 300:
            categories["should_exclude"].append(asset)
        elif "productitem" in name or "product" in name:
            categories["ui_components"].append(asset)
        elif figma_type in ["COMPONENT", "INSTANCE"] and max_dim > 50:
            categories["ui_components"].append(asset)
        elif "vector" in name and max_dim <= 32:
            categories["generic_vectors"].append(asset)
        elif max_dim > 200:
            categories["large_backgrounds"].append(asset)
        else:
            categories["generic_vectors"].append(asset)
    
    # Report categorization
    print("📊 IMPROVED CATEGORIZATION:")
    print("-" * 30)
    
    for category, items in categories.items():
        print(f"\n{category.replace('_', ' ').title()}: {len(items)}")
        if items:
            # Show examples with more detail
            for i, item in enumerate(items[:3]):
                print(f"  {i+1}. {item['name']} ({item['width']}x{item['height']}px)")
                print(f"     Type: {item['type']}, Figma: {item['figma_type']}")
            if len(items) > 3:
                print(f"     ... and {len(items) - 3} more")
    
    print()
    
    # Specific e-commerce analysis
    print("🛒 E-COMMERCE SPECIFIC ANALYSIS:")
    print("-" * 35)
    
    # Find heart/favorite icons
    heart_icons = [a for a in assets if "heart" in a["name"].lower()]
    print(f"❤️  Heart/Favorite Icons: {len(heart_icons)}")
    for heart in heart_icons[:3]:
        print(f"   • {heart['name']} ({heart['width']}x{heart['height']}px)")
    
    # Find product-related components
    product_components = [a for a in assets if "product" in a["name"].lower()]
    print(f"📦 Product Components: {len(product_components)}")
    for product in product_components[:3]:
        print(f"   • {product['name']} ({product['width']}x{product['height']}px)")
    
    # Find sale/promotional elements
    sale_elements = [a for a in assets if any(term in a["name"].lower() for term in ["sale", "onsale", "newsale"])]
    print(f"🏷️  Sale/Promotional Elements: {len(sale_elements)}")
    for sale in sale_elements:
        print(f"   • {sale['name']} ({sale['width']}x{sale['height']}px)")
    
    # Find navigation elements
    nav_elements = [a for a in assets if any(term in a["name"].lower() for term in ["arrow", "back", "action", "wifi", "signal"])]
    print(f"🧭 Navigation/Status Elements: {len(nav_elements)}")
    for nav in nav_elements[:3]:
        print(f"   • {nav['name']} ({nav['width']}x{nav['height']}px)")
    
    print()
    
    # Show filtering recommendations
    print("💡 FILTERING RECOMMENDATIONS FOR E-COMMERCE:")
    print("-" * 45)
    
    meaningful_count = len(categories["meaningful_icons"]) + len(categories["e_commerce_elements"])
    ui_count = len(categories["ui_components"])
    generic_count = len(categories["generic_vectors"])
    exclude_count = len(categories["should_exclude"])
    
    print(f"✅ KEEP ({meaningful_count + ui_count} assets):")
    print(f"   • {len(categories['meaningful_icons'])} meaningful icons (arrows, wifi, signals)")
    print(f"   • {len(categories['e_commerce_elements'])} e-commerce elements (hearts, favorites)")
    print(f"   • {ui_count} UI components (product items, sale badges)")
    
    print(f"\n⚠️  REVIEW ({generic_count} assets):")
    print(f"   • {generic_count} generic vectors (may need better naming)")
    print(f"   • Consider filtering out unnamed 'Vector' elements")
    
    print(f"\n🚫 EXCLUDE ({exclude_count} assets):")
    print(f"   • {exclude_count} large background elements")
    print(f"   • Full-screen backgrounds and containers")
    
    print()
    
    # Show improved extraction example
    print("🎯 IMPROVED EXTRACTION EXAMPLE:")
    print("-" * 35)
    
    print("```python")
    print("# For e-commerce product interface")
    print("assets = get_figma_assets_data_filtered(")
    print("    figma_link, access_token,")
    print("    include_images=False,  # Skip large product images")
    print("    include_icons=True,    # Keep UI icons")
    print("    include_components=True,  # Keep product components")
    print("    min_size=16,          # Skip tiny decorative elements")
    print("    exclude_patterns=[")
    print("        r'\\bbackground\\b',  # Exclude backgrounds")
    print("        r'\\bvector\\b',      # Exclude generic vectors")
    print("        r'\\bline\\s+\\d+\\b'  # Exclude generic lines")
    print("    ]")
    print(")")
    print("```")
    
    print()
    
    # Expected results
    print("📈 EXPECTED IMPROVEMENTS:")
    print("-" * 25)
    
    current_meaningful = len([a for a in assets if IMPROVED_ICON_NAME_REGEX.search(a["name"])])
    current_generic = len([a for a in assets if "vector" in a["name"].lower()])
    
    print(f"✅ Meaningful assets captured: {current_meaningful}/{len(assets)} ({current_meaningful/len(assets)*100:.1f}%)")
    print(f"⚠️  Generic vectors to review: {current_generic}")
    print(f"🎯 Focus on: Heart icons, product components, navigation elements")
    print(f"🚫 Filter out: Large backgrounds, generic vectors, unnamed elements")
    
    return assets, categories

def main():
    """Main function"""
    assets, categories = analyze_sample3_assets()
    
    print("\n" + "="*60)
    print("🎉 CONCLUSION FOR SAMPLE3.JSON:")
    print("="*60)
    print()
    print("This e-commerce product interface shows the improved logic can:")
    print("✅ Identify meaningful e-commerce elements (hearts, product items)")
    print("✅ Distinguish between UI components and backgrounds")
    print("✅ Recognize navigation and status icons (arrows, wifi, signal)")
    print("✅ Filter out large background elements automatically")
    print("✅ Provide better categorization for decision making")
    print()
    print("The dynamic nature means it will work similarly on other")
    print("e-commerce interfaces, product catalogs, and shopping apps!")

if __name__ == "__main__":
    main()
