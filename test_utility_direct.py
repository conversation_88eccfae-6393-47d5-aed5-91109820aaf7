#!/usr/bin/env python3
"""
Direct test of the figma_utils extract_asset_nodes function
"""

import json
import sys
import os

def test_extract_asset_nodes():
    """Test the extract_asset_nodes function directly"""
    
    # Set environment variables for testing
    os.environ['FIGMA_MIN_CONFIDENCE'] = '0.4'  # Higher threshold
    os.environ['FIGMA_MAX_DUPLICATES'] = '2'    # Limit duplicates
    
    # Import the function directly
    sys.path.insert(0, '/home/<USER>/kavia/graphnode-backend-api')
    
    # Load sample data
    sample_file = sys.argv[1] if len(sys.argv) > 1 else "screen_1_49.json"
    
    try:
        with open(sample_file, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Sample file {sample_file} not found")
        return
    
    print(f"🔍 TESTING ACTUAL UTILITY - {sample_file}")
    print("=" * 60)
    
    # Convert to Figma API format
    figma_data = convert_to_figma_format(data)
    
    # Import and test the actual function
    try:
        # Import just the function we need, avoiding pydantic issues
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "figma_utils", 
            "/home/<USER>/kavia/graphnode-backend-api/app/utils/figma_utils.py"
        )
        figma_utils = importlib.util.module_from_spec(spec)
        
        # Mock the dependencies that cause import issues
        import types
        mock_module = types.ModuleType('mock')
        sys.modules['app.connection.tenant_middleware'] = mock_module
        sys.modules['app.core.Settings'] = mock_module
        sys.modules['app.models.uiux.figma_model'] = mock_module
        sys.modules['app.utils.datetime_utils'] = mock_module
        
        # Add mock functions
        mock_module.figma_access_token = "mock_token"
        mock_module.get_figma_access_token = lambda: "mock_token"
        mock_module.generate_timestamp = lambda: "2024-01-01"
        mock_module.ProcessingStatus = type('ProcessingStatus', (), {'COMPLETED': 'completed'})
        mock_module.FigmaFrameModel = type('FigmaFrameModel', (), {})
        
        spec.loader.exec_module(figma_utils)
        
        # Test the extraction
        extracted_nodes = figma_utils.extract_asset_nodes(figma_data)
        
        print(f"✅ Successfully extracted {len(extracted_nodes)} assets")
        print()
        
        # Analyze results
        by_type = {}
        unique_names = set()
        
        for node in extracted_nodes:
            analysis = figma_utils._analyze_figma_node_patterns(node)
            asset_type = analysis.get('asset_type') or 'visual_element'
            name = node.get('name', 'Unnamed')
            
            if asset_type not in by_type:
                by_type[asset_type] = []
            by_type[asset_type].append({
                'name': name,
                'confidence': analysis.get('confidence', 0),
                'node': node
            })
            unique_names.add(name)
        
        # Display results
        for asset_type, assets in by_type.items():
            print(f"🎯 {asset_type.upper()} ASSETS ({len(assets)}):")
            name_counts = {}
            for asset in assets:
                name = asset['name']
                name_counts[name] = name_counts.get(name, 0) + 1
            
            for name, count in name_counts.items():
                first_asset = next(a for a in assets if a['name'] == name)
                confidence = first_asset['confidence']
                node = first_asset['node']
                bbox = node.get('absoluteBoundingBox', {})
                size = f"{bbox.get('width', 0):.0f}x{bbox.get('height', 0):.0f}px"
                
                if count > 1:
                    print(f"  • {name} ({count} instances) - {size} - confidence: {confidence:.2f}")
                else:
                    print(f"  • {name} - {size} - confidence: {confidence:.2f}")
            print()
        
        print(f"📊 SUMMARY:")
        print(f"Total assets: {len(extracted_nodes)}")
        print(f"Unique names: {len(unique_names)}")
        
        print(f"\n📝 UNIQUE NAMES LIST:")
        for i, name in enumerate(sorted(unique_names), 1):
            print(f"{i:2d}. {name}")
            
    except Exception as e:
        print(f"❌ Error testing utility: {e}")
        import traceback
        traceback.print_exc()

def convert_to_figma_format(data):
    """Convert processed data to Figma API format"""
    def convert_node(node):
        if not isinstance(node, dict):
            return None
        
        figma_node = {
            'id': node.get('id', ''),
            'name': node.get('name', ''),
            'type': node.get('figma_type', node.get('type', 'FRAME')),
            'visible': True,
            'fills': [],
            'strokes': [],
            'effects': [],
            'exportSettings': [],
            'children': []
        }
        
        # Convert dimensions
        if 'dimensions' in node:
            dims = node['dimensions']
            figma_node['absoluteBoundingBox'] = {
                'x': dims.get('x', 0),
                'y': dims.get('y', 0),
                'width': dims.get('width', 0),
                'height': dims.get('height', 0)
            }
        
        # Convert children
        for child in node.get('children', []):
            converted_child = convert_node(child)
            if converted_child:
                figma_node['children'].append(converted_child)
        
        return figma_node
    
    root_node = convert_node(data.get('root', {}))
    return {'document': root_node}

if __name__ == "__main__":
    test_extract_asset_nodes()
