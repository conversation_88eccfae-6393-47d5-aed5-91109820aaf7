# Figma Asset Extraction Improvements

## Overview
The Figma asset extraction utility has been significantly improved to better identify and extract important visual elements (images, icons, and components) while reducing unwanted figures.

## Key Problems Addressed

### 1. **Limited Icon Recognition**
**Before:** Only recognized basic icon naming patterns like `ic_`, `icon`, `mdi_`, etc.
**After:** Expanded to recognize 100+ icon-related terms including:
- Standard icon libraries: `lucide`, `feather`, `tabler`, `heroicon`, `material`, etc.
- Common icon names: `arrow`, `chevron`, `plus`, `minus`, `close`, `check`, `star`, etc.
- UI elements: `menu`, `search`, `filter`, `settings`, `bell`, `user`, etc.
- Action icons: `play`, `pause`, `download`, `share`, `save`, etc.

### 2. **Restrictive Size Limits**
**Before:** Icons limited to 256px, missing larger important icons
**After:** 
- Default max icon size increased to 512px
- Allows icons up to 4x the limit (2048px) for vectors and logos
- Configurable size limits for different use cases

### 3. **Missing Asset Types**
**Before:** Only extracted basic vectors and image fills
**After:** Now extracts:
- IMAGE type nodes
- COMPONENT and INSTANCE nodes (icon systems)
- Nodes with exportSettings (designer-marked assets)
- TEXT nodes with icon fonts
- Elements with visual content (fills, strokes, effects)

### 4. **Poor Categorization**
**Before:** Simple image/icon binary classification
**After:** Enhanced categorization:
- `image`: Photo-like content and larger visual elements
- `icon`: Small vector graphics, symbols, and UI elements
- `component`: Reusable design components
- Additional metadata: dimensions, export settings, image fills

## New Functions

### `get_figma_assets_data_filtered()`
Advanced filtering function with options for:
- **Type filtering**: Include/exclude images, icons, components
- **Size filtering**: Minimum size requirements
- **Name filtering**: Exclude patterns like "background", "container", etc.
- **Custom exclusion patterns**: Regex patterns to filter unwanted elements

### Enhanced `extract_asset_nodes()`
- More intelligent traversal of Figma node tree
- Better handling of nested assets
- Reduced false positives from structural elements
- Improved visibility and export settings detection

## Usage Examples

### Basic Extraction (Improved)
```python
from app.utils.figma_utils import get_figma_assets_data

assets = get_figma_assets_data(figma_link, access_token, max_icon_size=512)
print(f"Found {assets['counts']['total']} assets:")
print(f"- Images: {assets['counts']['images']}")
print(f"- Icons: {assets['counts']['icons']}")
print(f"- Components: {assets['counts']['components']}")
```

### Filtered Extraction (New)
```python
from app.utils.figma_utils import get_figma_assets_data_filtered

# Get only icons, excluding common unwanted patterns
icons_only = get_figma_assets_data_filtered(
    figma_link, 
    access_token,
    include_images=False,
    include_components=False,
    include_icons=True,
    exclude_patterns=[r'\bbackground\b', r'\bcontainer\b', r'\bframe\b']
)
```

### Large Images Only
```python
# Get only larger images (64px+), excluding small icons
large_images = get_figma_assets_data_filtered(
    figma_link,
    access_token,
    include_images=True,
    include_icons=False,
    include_components=False,
    min_size=64
)
```

## Testing

Use the provided test script to verify the improvements:

```bash
export FIGMA_FILE_LINK='https://www.figma.com/file/your-file-id/Your-File-Name'
export FIGMA_ACCESS_TOKEN='your-figma-access-token'
python test_figma_extraction.py
```

This will generate several JSON files showing:
- `basic_assets.json`: All extracted assets with new categorization
- `icons_only.json`: Only icon assets
- `images_only.json`: Only image assets (64px+)
- `filtered_assets.json`: Assets with common unwanted patterns excluded

## Configuration Options

### Environment Variables
- `FIGMA_ICON_MAX_WH`: Maximum width/height for icons (default: 512)
- `FIGMA_ICON_MAX_AREA`: Maximum area for icons (default: 512*512)
- `FIGMA_MIN_MAIN_IMG_WH`: Minimum size for main images (default: 32)
- `FIGMA_MIN_MAIN_IMG_AREA`: Minimum area for main images (default: 1024)
- `FIGMA_ASSET_TYPES`: Asset types to include: "icons,images,both" (default: "icons,images")

### Function Parameters
- `max_icon_size`: Maximum size for icon detection
- `include_images/icons/components`: Type filtering
- `min_size`: Minimum asset size
- `exclude_patterns`: List of regex patterns to exclude

## Expected Results

With these improvements, you should see:
- ✅ **More important icons captured**: UI elements, navigation icons, action buttons
- ✅ **Better image detection**: Photos, illustrations, larger visual elements
- ✅ **Fewer unwanted elements**: Reduced backgrounds, containers, spacers
- ✅ **Better categorization**: Clear distinction between images, icons, and components
- ✅ **More metadata**: Dimensions, export settings, image fill information
- ✅ **Flexible filtering**: Customize extraction for specific use cases

## Backward Compatibility

All existing function signatures remain the same, with new optional parameters having sensible defaults. Existing code will continue to work but benefit from the improved detection logic.
