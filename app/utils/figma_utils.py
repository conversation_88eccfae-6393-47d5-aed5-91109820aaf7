# app/utils/figma_utils.py
"""
Figma utilities for extracting assets and processing Figma API data.

Environment Variables for Configuration:
- FIGMA_MAX_ICON_SIZE: Maximum size for icon detection (default: 512)
- FIGMA_MEDIUM_SIZE_MULTIPLIER: Multiplier for medium icon size (default: 2)
- FIGMA_LARGE_SIZE_MULTIPLIER: Multiplier for large icon size (default: 4)
- FIGMA_MIN_SIZE: Minimum size for assets in filtered extraction (default: 16)
- FIGMA_MIN_CONFIDENCE: Minimum confidence threshold for dynamic asset detection (default: 0.3)
- FIGMA_MAX_AREA_MULTIPLIER: Maximum area multiplier for large element filtering (default: 100)
- FIGMA_MAX_DUPLICATES: Maximum number of duplicate assets with same name (default: 3)
- FIGMA_INCLUDE_IMAGES: Include image assets in filtered extraction (default: true)
- FIGMA_INCLUDE_ICONS: Include icon assets in filtered extraction (default: true)
- FIGMA_INCLUDE_COMPONENTS: Include component assets in filtered extraction (default: true)
- FIGMA_EXCLUDE_PATTERNS: Comma-separated regex patterns to exclude by name (default: empty)

Example:
    export FIGMA_MAX_ICON_SIZE=256
    export FIGMA_MIN_SIZE=32
    export FIGMA_EXCLUDE_PATTERNS="\\bbackground\\b,\\bcontainer\\b,\\bvector\\b"
"""

import requests
import re
import time
from typing import Optional, List, Dict, Any
from fastapi import HTTPException
from app.connection.tenant_middleware import figma_access_token
import json
import httpx
from app.models.uiux.figma_model import (
    ProcessingStatus,
    FigmaFrameModel,
)
import asyncio
from app.utils.datetime_utils import generate_timestamp


def get_figma_access_token():
    return figma_access_token.get()

def retry_request(func):
    def wrapper(*args, **kwargs):
        max_retries = 5
        retry_delay = 1
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise HTTPException(status_code=500, detail=f"Failed after {max_retries} attempts: {str(e)}")
                print(f"Request failed, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
    return wrapper

def extract_file_key(link: str) -> Optional[str]:
    """Extract the Figma file key from a given link."""
    file_match = re.search(r'file/([^/]+)', link)
    if file_match:
        return file_match.group(1)

    design_match = re.search(r'design/([^/]+)', link)
    if design_match:
        return design_match.group(1)

    return None

def extract_frame_data(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract frame data from the Figma API response recursively."""
    frames = []
    
    def find_frames(node: Dict[str, Any]):
        if node.get("type") == "FRAME":
            frames.append(node)
        
        if "children" in node:
            for child in node["children"]:
                find_frames(child)
    
    if figma_data.get("document"):
        find_frames(figma_data["document"])
        
    return frames
def extract_all_node_data(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract all node data from the Figma API response recursively."""
    nodes = []
    
    def find_nodes(node: Dict[str, Any]):
        # Add ALL nodes, not just frames
        nodes.append(node)
        
        if "children" in node:
            for child in node["children"]:
                find_nodes(child)
    
    if figma_data.get("document"):
        find_nodes(figma_data["document"])
        
    return nodes






@retry_request
def fetch_frame_images(file_key: str, frame_ids: List[str], limit: int = None) -> Dict[str, str]:
    """
    Fetch image URLs for multiple frames with retry logic and size validation.

    Args:
        file_key: Figma file key
        frame_ids: List of frame IDs to fetch
        limit: Size limit in KB (unused, kept for backward compatibility)
    """
    try:
        url = f"https://api.figma.com/v1/images/{file_key}"
        headers = {"X-Figma-Token": get_figma_access_token()}
        params = {"ids": ",".join(frame_ids)}
        response = requests.get(url, params=params, headers=headers, timeout=300)
        response.raise_for_status()

        images = response.json()['images']

        return images
    except Exception as e:
        raise ValueError(f"Error while fetching images: {str(e)}")

@retry_request
def fetch_frame_image(file_key: str, frame_id: str) -> Optional[str]:
    """Fetch image URL for a single frame with retry logic."""
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 2}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    return response.json()['images'].get(frame_id)

def get_frame_details(file_key: str, frame_id: str):
    # Fetch frame JSON data
    url = f"https://api.figma.com/v1/files/{file_key}/nodes?ids={frame_id}"
    headers = {"X-Figma-Token": get_figma_access_token()}
    
    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()
    frame_data = response.json()['nodes'][frame_id]['document']

    # Fetch frame image
    image_url = fetch_frame_image(file_key, frame_id)
    
    # Fetch frame thumbnail
    thumbnail_url = f"https://api.figma.com/v1/images/{file_key}?ids={frame_id}&scale=0.5&format=png"
    thumbnail_response = requests.get(thumbnail_url, headers=headers, timeout=300)
    thumbnail_response.raise_for_status()
    thumbnail_url = thumbnail_response.json()['images'].get(frame_id)

    return {
        "frame_id": frame_id,
        "file_key": file_key,
        "json_data": frame_data,
        "imageUrl": image_url,
        "thumbnailUrl": thumbnail_url
    }

def get_figma_file_data_limited(file_link, access_token, byte_limit=None, kb_limit=None, mb_limit=None):
    """
    Get Figma file data without size restrictions.

    Args:
        file_link (str): Figma file URL
        access_token (str): Figma access token
        byte_limit (int, optional): Ignored - kept for backward compatibility
        kb_limit (float, optional): Ignored - kept for backward compatibility
        mb_limit (float, optional): Ignored - kept for backward compatibility

    Returns:
        tuple: (response JSON data, size information dict)

    Raises:
        ValueError: If access token is invalid or file key cannot be extracted
    """
    # Parameters kept for backward compatibility but not used
    _ = byte_limit, kb_limit, mb_limit

    if type(access_token) == str:
        if len(access_token.strip()) == 0:
            raise ValueError("Figma access token is required")
        
    file_key = extract_file_key(file_link)
    
    response = requests.get(
        f"https://api.figma.com/v1/files/{file_key}",
        headers={"X-Figma-Token": access_token},
        timeout=300
    )
    response.raise_for_status()
    
    data = response.json()
    
    # Calculate actual file size for informational purposes
    json_str = json.dumps(data)
    size_bytes = len(json_str.encode('utf-8'))
    size_kb = size_bytes / 1024
    size_mb = size_kb / 1024
    
    sizes = {
        "byte_limit": None,  # No limits applied
        "kb_limit": None,    # No limits applied
        "mb_limit": None,    # No limits applied
        "size_bytes": size_bytes,
        "size_kb": size_kb,
        "size_mb": size_mb
    }
    
    return data, sizes

def get_figma_file_size(data):
    """Calculate the size of Figma file data in bytes, KB, and MB"""
    json_str = json.dumps(data)
    size_bytes = len(json_str.encode('utf-8'))
    size_kb = size_bytes / 1024
    size_mb = size_kb / 1024
    return {
        "size_bytes": size_bytes,
        "size_kb": size_kb,
        "size_mb": size_mb
    }
def save_json_to_logs(data: dict, filename: str = "figma_data.json") -> str:
    """
    Save JSON data to logs folder - simple and direct
    
    Args:
        data: Dictionary data to save
        filename: Name of the JSON file (default: figma_data.json)
    
    Returns:
        str: Path to saved file
    """
    import json
    import os
    
    # Create logs directory if it doesn't exist
    logs_dir = 'logs'
    os.makedirs(logs_dir, exist_ok=True)
    
    # Full file path (directory + filename)
    filepath = os.path.join(logs_dir, filename)
    
    # Save JSON file
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"Saved: {filepath}")
    return str(filepath)

async def get_figma_file_data_limited_async(client: httpx.AsyncClient, figma_link: str, figma_api_key: str, mb_limit: float = None) -> tuple:
    """
    Asynchronously fetch Figma file data with size limit check
    """
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": figma_api_key}

    # Check file size first
    try:
        head_response = await client.head(url, headers=headers)
        content_length = int(head_response.headers.get('content-length', 0))
        # Size checking disabled - kept for potential future use
        # size_mb = content_length / (1024 * 1024)
            
    except Exception as e:
        print(f"Warning: Could not check file size: {str(e)}")
        # Continue anyway since this is just a precaution

    # Get actual file data
    response = await client.get(url, headers=headers)
    response.raise_for_status()
    data = response.json()
    # save_json_to_logs(data)

    # Calculate sizes
    sizes = {
        'size_kb': round(len(str(data).encode('utf-8')) / 1024, 2),
        'size_mb': round(len(str(data).encode('utf-8')) / (1024 * 1024), 2),
        'byte_limit': mb_limit * 1024 * 1024 if mb_limit else None,
        'mb_limit': mb_limit
    }

    return data, sizes

async def fetch_frame_images_async(client: httpx.AsyncClient, file_key: str, frame_ids: List[str]) -> Dict[str, str]:
    """
    Asynchronously fetch frame images from Figma API
    """
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {
        "ids": ",".join(frame_ids),
        "scale": 2,
        "format": "png"
    }
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = await client.get(url, params=params, headers=headers)
    response.raise_for_status()
    return response.json().get('images', {})

async def process_frame(
    frame: dict, file_key: str, image_urls: Dict[str, str]
) -> FigmaFrameModel:
    """
    Process individual frame and return frame model with status.

    Args:
        frame (dict): Frame data from Figma API
        file_key (str): Figma file key
        image_urls (Dict[str, str]): Pre-fetched image URLs

    Returns:
        FigmaFrameModel: Processed frame model with status
    """
    try:
        # Initialize frame model with base data
        frame_model = FigmaFrameModel(
            id=frame["id"],
            name=frame.get("name", "Untitled Frame"),
            status=ProcessingStatus.PROCESSING,
            time_updated=generate_timestamp(),
        )

        # Validate frame data
        if not frame["id"]:
            frame_model.status = ProcessingStatus.FAILED
            frame_model.error_message = "Invalid frame ID"
            return frame_model

        # Try to get image URL
        image_url = image_urls.get(frame["id"])

        if not image_url:
            # If no pre-fetched URL, try to fetch individually
            try:
                image_url = fetch_frame_image(file_key, frame["id"])
                await asyncio.sleep(0.1)  # Small delay to prevent rate limiting
            except Exception as e:
                image_url = None

        if image_url:
            frame_model.imageUrl = image_url
            frame_model.status = ProcessingStatus.COMPLETED
        else:
            frame_model.status = ProcessingStatus.FAILED
            frame_model.error_message = "Failed to fetch frame image"

        # Add any additional frame metadata if available
        if frame.get("absoluteBoundingBox"):
            frame_model.metadata = {
                "width": frame["absoluteBoundingBox"].get("width"),
                "height": frame["absoluteBoundingBox"].get("height"),
            }

        return frame_model

    except Exception as e:
        # Return failed frame model on any error
        return FigmaFrameModel(
            id=frame.get("id", "unknown"),
            name=frame.get("name", "Error Frame"),
            status=ProcessingStatus.FAILED,
            error_message=f"Error processing frame: {str(e)}",
            time_updated=generate_timestamp(),
        )


# =========================
# Assets-only extraction utilities (images and icons)
# =========================

def _analyze_figma_node_patterns(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze a Figma node to determine its asset characteristics based on Figma API properties.
    This is completely dynamic and based on actual Figma API structure.
    """
    analysis = {
        'is_visual_asset': False,
        'asset_type': None,
        'confidence': 0.0,
        'reasons': []
    }

    # Get node properties from Figma API
    node_type = node.get('type', '').upper()
    name = (node.get('name') or '').strip()
    fills = node.get('fills', [])
    export_settings = node.get('exportSettings', [])
    absolute_bounding_box = node.get('absoluteBoundingBox', {})
    children = node.get('children', [])

    # Calculate dimensions
    width = absolute_bounding_box.get('width', 0)
    height = absolute_bounding_box.get('height', 0)
    area = width * height if width and height else 0

    # Figma API node types that are typically visual assets
    VISUAL_NODE_TYPES = {'VECTOR', 'IMAGE', 'COMPONENT', 'INSTANCE', 'RECTANGLE', 'ELLIPSE', 'REGULAR_POLYGON'}

    # Check if it's a visual node type
    if node_type in VISUAL_NODE_TYPES:
        analysis['is_visual_asset'] = True
        analysis['confidence'] += 0.3
        analysis['reasons'].append(f'Visual node type: {node_type}')

    # Check for export settings (strong indicator of intended asset)
    if export_settings:
        analysis['is_visual_asset'] = True
        analysis['confidence'] += 0.4
        analysis['reasons'].append('Has export settings')

    # Check for image fills (strong indicator of image asset)
    has_image_fill = any(fill.get('type') == 'IMAGE' for fill in fills)
    if has_image_fill:
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'image'
        analysis['confidence'] += 0.5
        analysis['reasons'].append('Has image fill')

    # Analyze based on Figma node type
    if node_type == 'IMAGE':
        analysis['asset_type'] = 'image'
        analysis['confidence'] += 0.5
        analysis['reasons'].append('Figma IMAGE node')
    elif node_type == 'VECTOR':
        analysis['asset_type'] = 'icon'
        analysis['confidence'] += 0.3
        analysis['reasons'].append('Figma VECTOR node')
    elif node_type in {'COMPONENT', 'INSTANCE'}:
        analysis['asset_type'] = 'component'
        analysis['confidence'] += 0.3
        analysis['reasons'].append(f'Figma {node_type} node')

    # Size-based analysis (dynamic thresholds)
    if area > 0:
        if area < 1024:  # Very small (< 32x32)
            analysis['confidence'] += 0.2 if analysis['asset_type'] == 'icon' else -0.1
            analysis['reasons'].append('Very small size (likely icon)')
        elif area > 40000:  # Very large (> 200x200)
            if analysis['asset_type'] != 'image':
                analysis['confidence'] -= 0.2
                analysis['reasons'].append('Very large size (likely background/container)')

    # Name-based analysis (semantic, not hardcoded patterns)
    if name:
        name_lower = name.lower()

        # Check for semantic indicators
        if any(term in name_lower for term in ['icon', 'logo', 'symbol', 'glyph']):
            if analysis['asset_type'] != 'image':
                analysis['asset_type'] = 'icon'
            analysis['confidence'] += 0.2
            analysis['reasons'].append('Name indicates icon/symbol')

        if any(term in name_lower for term in ['image', 'photo', 'picture', 'img']):
            analysis['asset_type'] = 'image'
            analysis['confidence'] += 0.2
            analysis['reasons'].append('Name indicates image')

        if any(term in name_lower for term in ['background', 'bg', 'container', 'wrapper', 'frame']):
            analysis['confidence'] -= 0.3
            analysis['reasons'].append('Name indicates structural element')

        # Generic names reduce confidence significantly
        generic_patterns = [
            r'^vector$', r'^rectangle\s*\d*$', r'^ellipse\s*\d*$', r'^group\s*\d*$',
            r'^frame\s*\d*$', r'^line\s*\d*$', r'^shape\s*\d*$', r'^path\s*\d*$'
        ]
        import re
        if any(re.match(pattern, name_lower) for pattern in generic_patterns):
            analysis['confidence'] -= 0.4
            analysis['reasons'].append('Generic/numbered name')

        # Meaningful names boost confidence
        meaningful_terms = [
            'heart', 'star', 'home', 'user', 'search', 'settings', 'menu', 'arrow',
            'wifi', 'signal', 'battery', 'phone', 'mail', 'chat', 'message',
            'play', 'pause', 'stop', 'next', 'prev', 'like', 'share', 'save'
        ]
        if any(term in name_lower for term in meaningful_terms):
            analysis['confidence'] += 0.3
            analysis['reasons'].append('Meaningful semantic name')

    # Children analysis
    if children:
        # If it has many children, it's likely a container
        if len(children) > 3:
            analysis['confidence'] -= 0.2
            analysis['reasons'].append('Has many children (likely container)')

    # Final confidence adjustment
    analysis['confidence'] = max(0.0, min(1.0, analysis['confidence']))

    return analysis

ICON_TYPES = {
    "VECTOR",
    "LINE",
    "REGULAR_POLYGON",
    "STAR",
    "BOOLEAN_OPERATION",
    "POLYGON",
    "ELLIPSE",
    "RECTANGLE",
    "IMAGE",  # Include IMAGE type for icon images
    "COMPONENT",
    "INSTANCE",
}

def _node_visible(node: Dict[str, Any]) -> bool:
    return node.get("visible", True) is not False

# Removed unused helper functions - now using dynamic analysis in _analyze_figma_node_patterns

def _has_image_fill(node: Dict[str, Any]) -> bool:
    fills = node.get("fills")
    if not isinstance(fills, list):
        return False
    for fill in fills:
        if isinstance(fill, dict) and fill.get("type") == "IMAGE" and fill.get("visible", True):
            return True
    return False

def _is_icon_like(node: Dict[str, Any], max_icon_size: int) -> bool:
    """
    Determine if a Figma node is likely an icon using dynamic analysis.
    Uses the new _analyze_figma_node_patterns function for intelligent detection.
    """
    analysis = _analyze_figma_node_patterns(node)

    # Consider it icon-like if:
    # 1. Analysis indicates it's a visual asset with reasonable confidence
    # 2. It's specifically identified as an icon or component
    # 3. It meets size constraints

    if not analysis['is_visual_asset'] or analysis['confidence'] < 0.2:
        return False

    # Check size constraints
    absolute_bounding_box = node.get('absoluteBoundingBox', {})
    width = absolute_bounding_box.get('width', 0)
    height = absolute_bounding_box.get('height', 0)

    if width and height:
        max_dimension = max(width, height)
        # Use dynamic size limits based on environment or defaults
        import os
        try:
            size_multiplier = float(os.getenv('FIGMA_LARGE_SIZE_MULTIPLIER', '4'))
            effective_max_size = max_icon_size * size_multiplier
        except (ValueError, TypeError):
            effective_max_size = max_icon_size * 4

        if max_dimension > effective_max_size:
            return False

    # Accept if it's identified as icon or component with good confidence
    return analysis['asset_type'] in ['icon', 'component'] or analysis['confidence'] >= 0.4

def extract_asset_nodes(figma_data: Dict[str, Any], max_icon_size: int = None) -> List[Dict[str, Any]]:
    """
    Extract only assets (images and icons) from the Figma file JSON.
    - Images: Nodes that have IMAGE fills or are IMAGE type.
    - Icons: Vector-like nodes, components with icon-like names, or exportable elements.
    - Visual elements: Important visual components that could be assets.

    Args:
        figma_data: The Figma API response data
        max_icon_size: Maximum size for icon detection (default from env FIGMA_MAX_ICON_SIZE or 512)

    Returns list of node dicts (as-is from Figma JSON) filtered to assets only.
    """
    if not isinstance(figma_data, dict):
        return []

    # Get configurable max icon size
    if max_icon_size is None:
        import os
        try:
            max_icon_size = int(os.getenv('FIGMA_MAX_ICON_SIZE', '512'))
        except (ValueError, TypeError):
            max_icon_size = 512

    assets: List[Dict[str, Any]] = []
    seen_ids: set = set()
    seen_names: Dict[str, int] = {}

    def _should_include_node(node: Dict[str, Any]) -> bool:
        """Use dynamic analysis to determine if node should be included as an asset"""
        analysis = _analyze_figma_node_patterns(node)

        # Get configurable confidence threshold
        import os
        try:
            min_confidence = float(os.getenv('FIGMA_MIN_CONFIDENCE', '0.3'))
        except (ValueError, TypeError):
            min_confidence = 0.3

        return analysis['is_visual_asset'] and analysis['confidence'] >= min_confidence

    def _should_deduplicate(node: Dict[str, Any], seen_names: Dict[str, int]) -> bool:
        """Check if this node should be skipped due to deduplication rules"""
        name = (node.get('name') or '').strip().lower()

        # Get deduplication settings
        import os
        try:
            max_duplicates = int(os.getenv('FIGMA_MAX_DUPLICATES', '3'))
        except (ValueError, TypeError):
            max_duplicates = 3

        # Skip if we've seen too many of this name
        if name in seen_names and seen_names[name] >= max_duplicates:
            return True

        # Always skip generic numbered elements after first instance
        import re
        generic_patterns = [
            r'^vector$', r'^rectangle\s*\d+$', r'^ellipse\s*\d+$',
            r'^line\s*\d+$', r'^group\s*\d+$', r'^frame\s*\d+$'
        ]
        if any(re.match(pattern, name) for pattern in generic_patterns):
            if name in seen_names and seen_names[name] >= 1:
                return True

        return False

    def traverse(node: Dict[str, Any]):
        if not isinstance(node, dict):
            return
        if not _node_visible(node):
            return

        node_id = node.get("id")
        if not node_id:
            # Continue traversal to children even if id is missing
            for child in node.get("children", []) or []:
                traverse(child)
            return

        ntype = node.get("type")

        # Skip structural containers but traverse their children
        if ntype in ("PAGE", "CANVAS"):
            for child in node.get("children", []) or []:
                traverse(child)
            return

        # Use dynamic analysis for all node types
        if _should_include_node(node) and node_id not in seen_ids:
            # Check deduplication rules
            if not _should_deduplicate(node, seen_names):
                assets.append(node)
                seen_ids.add(node_id)

                # Track name frequency for deduplication
                name = (node.get('name') or '').strip().lower()
                seen_names[name] = seen_names.get(name, 0) + 1

        # Check children for nested assets
        for child in node.get("children", []) or []:
            traverse(child)

    document = figma_data.get("document")
    if document:
        traverse(document)

    return assets

def get_figma_assets_data(figma_link: str, access_token: str, max_icon_size: int = None) -> Dict[str, Any]:
    """
    Fetch only assets (images and icons) and return their rendered image URLs.

    Args:
        figma_link: Figma file URL
        access_token: Figma access token
        max_icon_size: Maximum size for icon detection (default from env FIGMA_MAX_ICON_SIZE or 512)

    Returns:
      {
        "fileKey": str,
        "counts": {"total": int, "images": int, "icons": int, "components": int},
        "assets": [
          {
            "id": str,
            "name": str,
            "type": str,        # Figma node type
            "kind": "image" | "icon" | "component",
            "imageUrl": Optional[str],
            "dimensions": Optional[dict],
            "hasExportSettings": bool,
            "hasImageFill": bool
          }, ...
        ]
      }
    """
    # Get configurable max icon size
    if max_icon_size is None:
        import os
        try:
            max_icon_size = int(os.getenv('FIGMA_MAX_ICON_SIZE', '512'))
        except (ValueError, TypeError):
            max_icon_size = 512

    file_key = extract_file_key(figma_link)
    if not file_key:
        raise ValueError("Invalid Figma link")

    headers = {"X-Figma-Token": access_token}

    # Get full file JSON once
    file_url = f"https://api.figma.com/v1/files/{file_key}"
    resp = requests.get(file_url, headers=headers, timeout=300)
    resp.raise_for_status()
    data = resp.json()

    # Extract assets-only nodes
    asset_nodes = extract_asset_nodes(data, max_icon_size=max_icon_size)
    if not asset_nodes:
        return {
            "fileKey": file_key,
            "counts": {"total": 0, "images": 0, "icons": 0, "components": 0},
            "assets": []
        }

    node_ids = [n["id"] for n in asset_nodes if "id" in n]

    # Resolve rendered images for those nodes
    images_url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": ",".join(node_ids), "format": "png", "scale": 2}
    img_resp = requests.get(images_url, headers=headers, params=params, timeout=300)
    img_resp.raise_for_status()
    image_map = img_resp.json().get("images", {})

    assets_out: List[Dict[str, Any]] = []
    image_count = 0
    icon_count = 0
    component_count = 0

    for node in asset_nodes:
        nid = node["id"]
        ntype = node.get("type")
        name = node.get("name") or ""
        has_image_fill = _has_image_fill(node)
        is_icon = _is_icon_like(node, max_icon_size)
        has_export_settings = bool(node.get("exportSettings"))

        # Better categorization logic
        if ntype == "IMAGE" or has_image_fill:
            if is_icon:
                kind = "icon"
                icon_count += 1
            else:
                kind = "image"
                image_count += 1
        elif ntype in ("COMPONENT", "INSTANCE"):
            kind = "component"
            component_count += 1
        elif is_icon or has_export_settings:
            kind = "icon"
            icon_count += 1
        else:
            # Default to image for other visual elements
            kind = "image"
            image_count += 1

        # Get dimensions if available
        bbox = node.get("absoluteBoundingBox", {})
        dimensions = None
        if bbox.get("width") and bbox.get("height"):
            dimensions = {
                "width": bbox["width"],
                "height": bbox["height"]
            }

        url = image_map.get(nid)

        assets_out.append({
            "id": nid,
            "name": name,
            "type": ntype,
            "kind": kind,
            "imageUrl": url,
            "dimensions": dimensions,
            "hasExportSettings": has_export_settings,
            "hasImageFill": has_image_fill
        })

    return {
        "fileKey": file_key,
        "counts": {
            "total": len(assets_out),
            "images": image_count,
            "icons": icon_count,
            "components": component_count
        },
        "assets": assets_out
    }

def get_figma_assets_data_filtered(
    figma_link: str,
    access_token: str,
    max_icon_size: int = None,
    include_images: bool = None,
    include_icons: bool = None,
    include_components: bool = None,
    min_size: int = None,
    exclude_patterns: List[str] = None
) -> Dict[str, Any]:
    """
    Enhanced version of get_figma_assets_data with filtering options.

    Args:
        figma_link: Figma file URL
        access_token: Figma access token
        max_icon_size: Maximum size for icons (default from env FIGMA_MAX_ICON_SIZE or 512)
        include_images: Whether to include image assets (default from env FIGMA_INCLUDE_IMAGES or True)
        include_icons: Whether to include icon assets (default from env FIGMA_INCLUDE_ICONS or True)
        include_components: Whether to include component assets (default from env FIGMA_INCLUDE_COMPONENTS or True)
        min_size: Minimum size for assets (default from env FIGMA_MIN_SIZE or 16)
        exclude_patterns: List of regex patterns to exclude by name (default from env FIGMA_EXCLUDE_PATTERNS)

    Returns:
        Dictionary with filtered assets data
    """
    import os

    # Get configurable defaults from environment variables
    if max_icon_size is None:
        try:
            max_icon_size = int(os.getenv('FIGMA_MAX_ICON_SIZE', '512'))
        except (ValueError, TypeError):
            max_icon_size = 512

    if include_images is None:
        include_images = os.getenv('FIGMA_INCLUDE_IMAGES', 'true').lower() == 'true'

    if include_icons is None:
        include_icons = os.getenv('FIGMA_INCLUDE_ICONS', 'true').lower() == 'true'

    if include_components is None:
        include_components = os.getenv('FIGMA_INCLUDE_COMPONENTS', 'true').lower() == 'true'

    if min_size is None:
        try:
            min_size = int(os.getenv('FIGMA_MIN_SIZE', '16'))
        except (ValueError, TypeError):
            min_size = 16

    if exclude_patterns is None:
        exclude_env = os.getenv('FIGMA_EXCLUDE_PATTERNS', '')
        if exclude_env:
            exclude_patterns = [pattern.strip() for pattern in exclude_env.split(',') if pattern.strip()]
        else:
            exclude_patterns = []

    # Get all assets first
    all_assets_data = get_figma_assets_data(figma_link, access_token, max_icon_size)

    if exclude_patterns is None:
        exclude_patterns = []

    # Filter assets based on criteria
    filtered_assets = []
    counts = {"total": 0, "images": 0, "icons": 0, "components": 0}

    for asset in all_assets_data["assets"]:
        # Check type inclusion
        kind = asset["kind"]
        if kind == "image" and not include_images:
            continue
        if kind == "icon" and not include_icons:
            continue
        if kind == "component" and not include_components:
            continue

        # Check size requirements
        dimensions = asset.get("dimensions")
        if dimensions:
            width = dimensions.get("width", 0)
            height = dimensions.get("height", 0)
            if width < min_size or height < min_size:
                continue

        # Check exclusion patterns
        name = asset.get("name", "").lower()
        if any(re.search(pattern, name, re.IGNORECASE) for pattern in exclude_patterns):
            continue

        # Asset passed all filters
        filtered_assets.append(asset)
        counts["total"] += 1
        counts[f"{kind}s"] += 1

    return {
        "fileKey": all_assets_data["fileKey"],
        "counts": counts,
        "assets": filtered_assets,
        "filters_applied": {
            "max_icon_size": max_icon_size,
            "include_images": include_images,
            "include_icons": include_icons,
            "include_components": include_components,
            "min_size": min_size,
            "exclude_patterns": exclude_patterns
        }
    }
