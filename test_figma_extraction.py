#!/usr/bin/env python3
"""
Test script for improved Figma asset extraction.
This script helps you test the enhanced figma_utils.py functions.
"""

import os
import sys
import json
from typing import Dict, Any

# Add the app directory to the path so we can import the utils
sys.path.append('app')

from utils.figma_utils import (
    get_figma_assets_data,
    get_figma_assets_data_filtered,
    extract_asset_nodes,
    get_figma_file_data_limited
)

def test_asset_extraction(figma_link: str, access_token: str):
    """Test the improved asset extraction functions"""
    
    print("🔍 Testing Figma Asset Extraction")
    print("=" * 50)
    print(f"Figma Link: {figma_link}")
    print()
    
    try:
        # Test 1: Basic asset extraction
        print("📋 Test 1: Basic Asset Extraction")
        print("-" * 30)
        
        basic_assets = get_figma_assets_data(figma_link, access_token, max_icon_size=512)
        
        print(f"File Key: {basic_assets['fileKey']}")
        print(f"Total Assets: {basic_assets['counts']['total']}")
        print(f"Images: {basic_assets['counts']['images']}")
        print(f"Icons: {basic_assets['counts']['icons']}")
        print(f"Components: {basic_assets['counts']['components']}")
        print()
        
        # Show some examples
        print("📝 Sample Assets:")
        for i, asset in enumerate(basic_assets['assets'][:10]):  # Show first 10
            print(f"  {i+1}. {asset['name']} ({asset['type']}) - {asset['kind']}")
            if asset.get('dimensions'):
                dims = asset['dimensions']
                print(f"     Size: {dims['width']}x{dims['height']}px")
            print(f"     Has Image URL: {'Yes' if asset.get('imageUrl') else 'No'}")
            print(f"     Has Export Settings: {'Yes' if asset.get('hasExportSettings') else 'No'}")
            print()
        
        if len(basic_assets['assets']) > 10:
            print(f"... and {len(basic_assets['assets']) - 10} more assets")
        print()
        
        # Test 2: Filtered extraction (icons only)
        print("🎯 Test 2: Icons Only")
        print("-" * 20)
        
        icons_only = get_figma_assets_data_filtered(
            figma_link, 
            access_token,
            include_images=False,
            include_components=False,
            include_icons=True,
            min_size=8  # Very small minimum to catch all icons
        )
        
        print(f"Icons Found: {icons_only['counts']['icons']}")
        print("Icon Examples:")
        for i, asset in enumerate(icons_only['assets'][:5]):
            print(f"  {i+1}. {asset['name']} ({asset['type']})")
        print()
        
        # Test 3: Images only (larger assets)
        print("🖼️  Test 3: Images Only (min 64px)")
        print("-" * 35)
        
        images_only = get_figma_assets_data_filtered(
            figma_link,
            access_token,
            include_images=True,
            include_components=False,
            include_icons=False,
            min_size=64
        )
        
        print(f"Images Found: {images_only['counts']['images']}")
        print("Image Examples:")
        for i, asset in enumerate(images_only['assets'][:5]):
            print(f"  {i+1}. {asset['name']} ({asset['type']})")
            if asset.get('dimensions'):
                dims = asset['dimensions']
                print(f"     Size: {dims['width']}x{dims['height']}px")
        print()
        
        # Test 4: Exclude common unwanted patterns
        print("🚫 Test 4: Filtered (excluding backgrounds, containers, etc.)")
        print("-" * 60)
        
        filtered_assets = get_figma_assets_data_filtered(
            figma_link,
            access_token,
            exclude_patterns=[
                r'\bbackground\b', r'\bbg\b', r'\bcontainer\b', 
                r'\bframe\b', r'\bdivider\b', r'\bseparator\b',
                r'\bspacer\b', r'\bmask\b', r'\boverlay\b'
            ]
        )
        
        print(f"Filtered Assets: {filtered_assets['counts']['total']}")
        print(f"Images: {filtered_assets['counts']['images']}")
        print(f"Icons: {filtered_assets['counts']['icons']}")
        print(f"Components: {filtered_assets['counts']['components']}")
        print()
        
        # Save results to files for inspection
        print("💾 Saving Results")
        print("-" * 15)
        
        with open('basic_assets.json', 'w') as f:
            json.dump(basic_assets, f, indent=2)
        print("✅ Saved basic_assets.json")
        
        with open('icons_only.json', 'w') as f:
            json.dump(icons_only, f, indent=2)
        print("✅ Saved icons_only.json")
        
        with open('images_only.json', 'w') as f:
            json.dump(images_only, f, indent=2)
        print("✅ Saved images_only.json")
        
        with open('filtered_assets.json', 'w') as f:
            json.dump(filtered_assets, f, indent=2)
        print("✅ Saved filtered_assets.json")
        
        print()
        print("🎉 Testing Complete!")
        print("Check the generated JSON files to see the extracted assets.")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main function to run the test"""
    
    # Get environment variables
    figma_link = os.getenv('FIGMA_FILE_LINK')
    access_token = os.getenv('FIGMA_ACCESS_TOKEN')
    
    if not figma_link or not access_token:
        print("❌ Please set FIGMA_FILE_LINK and FIGMA_ACCESS_TOKEN environment variables")
        print()
        print("Example:")
        print("export FIGMA_FILE_LINK='https://www.figma.com/file/your-file-id/Your-File-Name'")
        print("export FIGMA_ACCESS_TOKEN='your-figma-access-token'")
        print("python test_figma_extraction.py")
        sys.exit(1)
    
    test_asset_extraction(figma_link, access_token)

if __name__ == "__main__":
    main()
