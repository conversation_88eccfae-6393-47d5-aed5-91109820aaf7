#!/usr/bin/env python3
"""
Test the new dynamic Figma asset extraction approach.
This demonstrates how the system now uses Figma API structure analysis
instead of hardcoded patterns.
"""

import json
import sys
from typing import Dict, Any

def _analyze_figma_node_patterns(node: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze a Figma node to determine its asset characteristics based on Figma API properties.
    This is completely dynamic and based on actual Figma API structure.
    """
    analysis = {
        'is_visual_asset': False,
        'asset_type': None,
        'confidence': 0.0,
        'reasons': []
    }

    # Get node properties from Figma API
    node_type = node.get('type', '').upper()
    name = (node.get('name') or '').strip()
    fills = node.get('fills', [])
    export_settings = node.get('exportSettings', [])
    absolute_bounding_box = node.get('absoluteBoundingBox', {})
    children = node.get('children', [])

    # Calculate dimensions
    width = absolute_bounding_box.get('width', 0)
    height = absolute_bounding_box.get('height', 0)
    area = width * height if width and height else 0

    # Figma API node types that are typically visual assets
    VISUAL_NODE_TYPES = {'VECTOR', 'IMAGE', 'COMPONENT', 'INSTANCE', 'RECTANGLE', 'ELLIPSE', 'REGULAR_POLYGON'}

    # Check if it's a visual node type
    if node_type in VISUAL_NODE_TYPES:
        analysis['is_visual_asset'] = True
        analysis['confidence'] += 0.3
        analysis['reasons'].append(f'Visual node type: {node_type}')

    # Check for export settings (strong indicator of intended asset)
    if export_settings:
        analysis['is_visual_asset'] = True
        analysis['confidence'] += 0.4
        analysis['reasons'].append('Has export settings')

    # Check for image fills (strong indicator of image asset)
    has_image_fill = any(fill.get('type') == 'IMAGE' for fill in fills)
    if has_image_fill:
        analysis['is_visual_asset'] = True
        analysis['asset_type'] = 'image'
        analysis['confidence'] += 0.5
        analysis['reasons'].append('Has image fill')

    # Analyze based on Figma node type
    if node_type == 'IMAGE':
        analysis['asset_type'] = 'image'
        analysis['confidence'] += 0.5
        analysis['reasons'].append('Figma IMAGE node')
    elif node_type == 'VECTOR':
        analysis['asset_type'] = 'icon'
        analysis['confidence'] += 0.3
        analysis['reasons'].append('Figma VECTOR node')
    elif node_type in {'COMPONENT', 'INSTANCE'}:
        analysis['asset_type'] = 'component'
        analysis['confidence'] += 0.3
        analysis['reasons'].append(f'Figma {node_type} node')

    # Size-based analysis (dynamic thresholds)
    if area > 0:
        if area < 1024:  # Very small (< 32x32)
            analysis['confidence'] += 0.2 if analysis['asset_type'] == 'icon' else -0.1
            analysis['reasons'].append('Very small size (likely icon)')
        elif area > 40000:  # Very large (> 200x200)
            if analysis['asset_type'] != 'image':
                analysis['confidence'] -= 0.2
                analysis['reasons'].append('Very large size (likely background/container)')

    # Name-based analysis (semantic, not hardcoded patterns)
    if name:
        name_lower = name.lower()

        # Check for semantic indicators
        if any(term in name_lower for term in ['icon', 'logo', 'symbol', 'glyph']):
            if analysis['asset_type'] != 'image':
                analysis['asset_type'] = 'icon'
            analysis['confidence'] += 0.2
            analysis['reasons'].append('Name indicates icon/symbol')

        if any(term in name_lower for term in ['image', 'photo', 'picture', 'img']):
            analysis['asset_type'] = 'image'
            analysis['confidence'] += 0.2
            analysis['reasons'].append('Name indicates image')

        if any(term in name_lower for term in ['background', 'bg', 'container', 'wrapper', 'frame']):
            analysis['confidence'] -= 0.3
            analysis['reasons'].append('Name indicates structural element')

        # Generic names reduce confidence
        if name_lower in ['vector', 'rectangle', 'ellipse', 'group', 'frame']:
            analysis['confidence'] -= 0.2
            analysis['reasons'].append('Generic name')

    # Children analysis
    if children:
        # If it has many children, it's likely a container
        if len(children) > 3:
            analysis['confidence'] -= 0.2
            analysis['reasons'].append('Has many children (likely container)')

    # Final confidence adjustment
    analysis['confidence'] = max(0.0, min(1.0, analysis['confidence']))

    return analysis

def test_dynamic_analysis():
    """Test the dynamic analysis on sample data"""

    # Load sample data
    sample_file = sys.argv[1] if len(sys.argv) > 1 else "sample3.json"

    try:
        with open(sample_file, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Sample file {sample_file} not found")
        return

    print(f"🔍 DYNAMIC FIGMA ANALYSIS - {sample_file}")
    print("=" * 60)
    print()

    # Collect all extracted assets
    extracted_assets = []

    def analyze_nodes(node, depth=0):
        if not isinstance(node, dict):
            return

        node_type = node.get('figma_type', node.get('type', 'UNKNOWN'))
        name = node.get('name', 'Unnamed')

        # Simulate Figma API structure for our processed data
        figma_node = {
            'type': node_type,
            'name': name,
            'absoluteBoundingBox': node.get('dimensions', {}),
            'fills': [],  # Would come from Figma API
            'exportSettings': [],  # Would come from Figma API
            'children': node.get('children', [])
        }

        # Add width/height to absoluteBoundingBox if available
        if 'dimensions' in node:
            dims = node['dimensions']
            figma_node['absoluteBoundingBox'] = {
                'x': dims.get('x', 0),
                'y': dims.get('y', 0),
                'width': dims.get('width', 0),
                'height': dims.get('height', 0)
            }

        # Analyze this node
        analysis = _analyze_figma_node_patterns(figma_node)

        if analysis['is_visual_asset'] and analysis['confidence'] >= 0.3:
            dims = node.get('dimensions', {})
            extracted_assets.append({
                'name': name,
                'type': node_type,
                'asset_type': analysis['asset_type'],
                'confidence': analysis['confidence'],
                'width': dims.get('width', 0),
                'height': dims.get('height', 0),
                'depth': depth
            })

        # Recurse into children
        for child in node.get('children', []):
            analyze_nodes(child, depth + 1)

    # Analyze the sample data
    analyze_nodes(data.get('root', {}))

    # Display extracted asset names
    print("📋 EXTRACTED ASSET NAMES:")
    print("-" * 30)

    # Group by asset type
    by_type = {}
    for asset in extracted_assets:
        asset_type = asset['asset_type'] or 'visual_element'
        if asset_type not in by_type:
            by_type[asset_type] = []
        by_type[asset_type].append(asset)

    # Display by category
    for asset_type, assets in by_type.items():
        print(f"\n🎯 {asset_type.upper()} ASSETS ({len(assets)}):")
        unique_names = {}
        for asset in assets:
            name = asset['name']
            if name not in unique_names:
                unique_names[name] = []
            unique_names[name].append(asset)

        for name, instances in unique_names.items():
            count = len(instances)
            first_instance = instances[0]
            size_info = f"{first_instance['width']}x{first_instance['height']}px"
            confidence = first_instance['confidence']

            if count > 1:
                print(f"  • {name} ({count} instances) - {size_info} - confidence: {confidence:.2f}")
            else:
                print(f"  • {name} - {size_info} - confidence: {confidence:.2f}")

    # Summary
    print(f"\n📊 SUMMARY:")
    print(f"Total assets extracted: {len(extracted_assets)}")
    unique_names = set(asset['name'] for asset in extracted_assets)
    print(f"Unique asset names: {len(unique_names)}")

    print(f"\n📝 ALL UNIQUE NAMES:")
    for i, name in enumerate(sorted(unique_names), 1):
        print(f"{i:2d}. {name}")
    
    print("\n🎯 KEY ADVANTAGES OF DYNAMIC APPROACH:")
    print("-" * 40)
    print("✅ No hardcoded icon name patterns")
    print("✅ Uses actual Figma API node properties")
    print("✅ Analyzes node type, size, fills, export settings")
    print("✅ Confidence-based scoring system")
    print("✅ Configurable thresholds via environment variables")
    print("✅ Semantic analysis of names (not pattern matching)")
    print("✅ Size-based intelligence with dynamic thresholds")
    print("✅ Structural analysis (children, visual content)")
    
    print("\n🔧 CONFIGURATION:")
    print("-" * 15)
    print("Set environment variables to customize:")
    print("- FIGMA_MIN_CONFIDENCE=0.3 (confidence threshold)")
    print("- FIGMA_MAX_ICON_SIZE=512 (max icon size)")
    print("- FIGMA_MAX_AREA_MULTIPLIER=100 (large element filter)")
    
    print("\n💡 EXAMPLE USAGE:")
    print("-" * 15)
    print("```python")
    print("# Simple extraction with dynamic analysis")
    print("assets = get_figma_assets_data(figma_link, access_token)")
    print("")
    print("# Filtered extraction with custom confidence")
    print("import os")
    print("os.environ['FIGMA_MIN_CONFIDENCE'] = '0.4'")
    print("assets = get_figma_assets_data_filtered(figma_link, access_token)")
    print("```")

if __name__ == "__main__":
    test_dynamic_analysis()
